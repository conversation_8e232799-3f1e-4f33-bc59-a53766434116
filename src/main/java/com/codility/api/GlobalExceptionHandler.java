package com.codility.api;

import com.codility.service.TradingService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

/**
 * Global exception handler for the trading application.
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(TradingService.InsufficientBuyingPowerException.class)
    public ResponseEntity<ErrorResponse> handleInsufficientBuyingPower(TradingService.InsufficientBuyingPowerException e) {
        return ResponseEntity.badRequest()
                .body(new ErrorResponse(400, "Insufficient buying power"));
    }

    @ExceptionHandler(TradingService.InsufficientInventoryException.class)
    public ResponseEntity<ErrorResponse> handleInsufficientInventory(TradingService.InsufficientInventoryException e) {
        return ResponseEntity.badRequest()
                .body(new ErrorResponse(400, "Insufficient inventory"));
    }

    @ExceptionHandler(TradingService.OrderNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleOrderNotFound(TradingService.OrderNotFoundException e) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new ErrorResponse(404, "Order not found"));
    }

    @ExceptionHandler(TradingService.OrderCannotBeCancelledException.class)
    public ResponseEntity<ErrorResponse> handleOrderCannotBeCancelled(TradingService.OrderCannotBeCancelledException e) {
        return ResponseEntity.badRequest()
                .body(new ErrorResponse(400, "Order cannot be cancelled"));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse(500, "Internal server error"));
    }
}
