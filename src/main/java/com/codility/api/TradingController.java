package com.codility.api;

import com.codility.repository.OrderEntity;
import com.codility.service.TradingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * This is the entry point for all trading operations.
 */
@RestController
@RequestMapping("/orders")
public class TradingController {

    @Autowired
    private TradingService tradingService;

    /**
     * Creates a new order (buy or sell).
     */
    @PostMapping
    public ResponseEntity<?> createOrder(@RequestBody OrderRequest request) {
        try {
            OrderEntity order = tradingService.createOrder(
                    request.getPortfolioId(),
                    request.getIsin(),
                    request.getSide(),
                    request.getQuantity()
            );

            OrderResponse response = new OrderResponse(
                    order.getId(),
                    order.getPortfolioId(),
                    order.getIsin(),
                    order.getSide(),
                    order.getQuantity(),
                    order.getPrice(),
                    order.getStatus()
            );

            return ResponseEntity.ok(response);

        } catch (TradingService.InsufficientBuyingPowerException e) {
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse(400, "Insufficient buying power"));
        } catch (TradingService.InsufficientInventoryException e) {
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse(400, "Insufficient inventory"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse(500, "Internal server error"));
        }
    }

    /**
     * Retrieves an order by ID.
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getOrder(@PathVariable Long id) {
        Optional<OrderEntity> orderOpt = tradingService.getOrder(id);

        if (orderOpt.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ErrorResponse(404, "Order not found"));
        }

        OrderEntity order = orderOpt.get();
        OrderResponse response = new OrderResponse(
                order.getId(),
                order.getPortfolioId(),
                order.getIsin(),
                order.getSide(),
                order.getQuantity(),
                order.getPrice(),
                order.getStatus()
        );

        return ResponseEntity.ok(response);
    }

    /**
     * Cancels an order.
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> cancelOrder(@PathVariable Long id) {
        try {
            OrderEntity order = tradingService.cancelOrder(id);

            OrderResponse response = new OrderResponse(
                    order.getId(),
                    order.getPortfolioId(),
                    order.getIsin(),
                    order.getSide(),
                    order.getQuantity(),
                    order.getPrice(),
                    order.getStatus()
            );

            return ResponseEntity.ok(response);

        } catch (TradingService.OrderNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ErrorResponse(404, "Order not found"));
        } catch (TradingService.OrderCannotBeCancelledException e) {
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse(400, "Order cannot be cancelled"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse(500, "Internal server error"));
        }
    }
}
