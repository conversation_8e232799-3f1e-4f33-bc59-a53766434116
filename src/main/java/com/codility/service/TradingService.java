package com.codility.service;

import com.codility.MarketDataService;
import com.codility.OrderSide;
import com.codility.OrderStatus;
import com.codility.api.OrderResponse;
import com.codility.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * Service for handling trading business logic.
 */
@Service
@Transactional
public class TradingService {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private BuyingPowerRepository buyingPowerRepository;

    @Autowired
    private InventoryRepository inventoryRepository;

    @Autowired
    private MarketDataService marketDataService;

    /**
     * Creates a new order (buy or sell).
     */
    public OrderEntity createOrder(String portfolioId, String isin, OrderSide side, BigDecimal quantity) {
        BigDecimal price = marketDataService.getPrice(isin);
        
        if (side == OrderSide.BUY) {
            return createBuyOrder(portfolioId, isin, quantity, price);
        } else {
            return createSellOrder(portfolioId, isin, quantity, price);
        }
    }

    /**
     * Creates a buy order if sufficient buying power exists.
     */
    private OrderEntity createBuyOrder(String portfolioId, String isin, BigDecimal quantity, BigDecimal price) {
        BigDecimal totalCost = quantity.multiply(price);

        // Check buying power
        BuyingPowerEntity buyingPower = buyingPowerRepository.findById(portfolioId)
                .orElseThrow(() -> new InsufficientBuyingPowerException("Portfolio not found"));

        if (buyingPower.getAmount().compareTo(totalCost) < 0) {
            throw new InsufficientBuyingPowerException("Insufficient buying power");
        }

        // Create order
        OrderEntity order = new OrderEntity(portfolioId, isin, OrderStatus.CREATED, OrderSide.BUY, quantity, price);
        order = orderRepository.save(order);

        // Update buying power
        BigDecimal newBuyingPower = buyingPower.getAmount().subtract(totalCost);
        buyingPowerRepository.save(new BuyingPowerEntity(portfolioId, newBuyingPower));

        // Update inventory
        updateInventory(portfolioId, isin, quantity);

        return order;
    }

    /**
     * Creates a sell order if sufficient inventory exists.
     */
    private OrderEntity createSellOrder(String portfolioId, String isin, BigDecimal quantity, BigDecimal price) {
        // Check inventory
        InventoryEntity inventory = inventoryRepository.findById(new InventoryEntityId(portfolioId, isin))
                .orElse(new InventoryEntity(portfolioId, isin, BigDecimal.ZERO));
        
        if (inventory.getQuantity().compareTo(quantity) < 0) {
            throw new InsufficientInventoryException("Insufficient inventory");
        }
        
        // Create order
        OrderEntity order = new OrderEntity(portfolioId, isin, OrderStatus.CREATED, OrderSide.SELL, quantity, price);
        order = orderRepository.save(order);
        
        // Update inventory
        BigDecimal newQuantity = inventory.getQuantity().subtract(quantity);
        inventoryRepository.save(new InventoryEntity(portfolioId, isin, newQuantity));
        
        // Update buying power
        BigDecimal proceeds = quantity.multiply(price);
        BuyingPowerEntity buyingPower = buyingPowerRepository.findById(portfolioId)
                .orElseThrow(() -> new InsufficientInventoryException("Portfolio not found"));
        BigDecimal newBuyingPower = buyingPower.getAmount().add(proceeds);
        buyingPowerRepository.save(new BuyingPowerEntity(portfolioId, newBuyingPower));
        
        return order;
    }

    /**
     * Updates inventory by adding the specified quantity.
     */
    private void updateInventory(String portfolioId, String isin, BigDecimal quantity) {
        InventoryEntity inventory = inventoryRepository.findById(new InventoryEntityId(portfolioId, isin))
                .orElse(new InventoryEntity(portfolioId, isin, BigDecimal.ZERO));
        
        BigDecimal newQuantity = inventory.getQuantity().add(quantity);
        inventoryRepository.save(new InventoryEntity(portfolioId, isin, newQuantity));
    }

    /**
     * Retrieves an order by ID.
     */
    public Optional<OrderEntity> getOrder(Long orderId) {
        return orderRepository.findById(orderId);
    }

    /**
     * Cancels an order if it's in CREATED status.
     */
    public OrderEntity cancelOrder(Long orderId) {
        OrderEntity order = orderRepository.findById(orderId)
                .orElseThrow(() -> new OrderNotFoundException("Order not found"));
        
        if (order.getStatus() != OrderStatus.CREATED) {
            throw new OrderCannotBeCancelledException("Order cannot be cancelled");
        }
        
        // Revert the changes made during order creation
        if (order.getSide() == OrderSide.BUY) {
            revertBuyOrder(order);
        } else {
            revertSellOrder(order);
        }
        
        // Update order status
        order.setStatus(OrderStatus.CANCELLED);
        return orderRepository.save(order);
    }

    /**
     * Reverts changes made by a buy order.
     */
    private void revertBuyOrder(OrderEntity order) {
        String portfolioId = order.getPortfolioId();
        String isin = order.getIsin();
        BigDecimal quantity = order.getQuantity();
        BigDecimal price = order.getPrice();
        BigDecimal totalCost = quantity.multiply(price);
        
        // Restore buying power
        BuyingPowerEntity buyingPower = buyingPowerRepository.findById(portfolioId)
                .orElseThrow(() -> new OrderCannotBeCancelledException("Portfolio not found"));
        BigDecimal newBuyingPower = buyingPower.getAmount().add(totalCost);
        buyingPowerRepository.save(new BuyingPowerEntity(portfolioId, newBuyingPower));
        
        // Remove from inventory
        InventoryEntity inventory = inventoryRepository.findById(new InventoryEntityId(portfolioId, isin))
                .orElse(new InventoryEntity(portfolioId, isin, BigDecimal.ZERO));
        BigDecimal newQuantity = inventory.getQuantity().subtract(quantity);
        inventoryRepository.save(new InventoryEntity(portfolioId, isin, newQuantity));
    }

    /**
     * Reverts changes made by a sell order.
     */
    private void revertSellOrder(OrderEntity order) {
        String portfolioId = order.getPortfolioId();
        String isin = order.getIsin();
        BigDecimal quantity = order.getQuantity();
        BigDecimal price = order.getPrice();
        BigDecimal proceeds = quantity.multiply(price);
        
        // Reduce buying power
        BuyingPowerEntity buyingPower = buyingPowerRepository.findById(portfolioId)
                .orElseThrow(() -> new OrderCannotBeCancelledException("Portfolio not found"));
        BigDecimal newBuyingPower = buyingPower.getAmount().subtract(proceeds);
        buyingPowerRepository.save(new BuyingPowerEntity(portfolioId, newBuyingPower));
        
        // Restore inventory
        InventoryEntity inventory = inventoryRepository.findById(new InventoryEntityId(portfolioId, isin))
                .orElse(new InventoryEntity(portfolioId, isin, BigDecimal.ZERO));
        BigDecimal newQuantity = inventory.getQuantity().add(quantity);
        inventoryRepository.save(new InventoryEntity(portfolioId, isin, newQuantity));
    }

    // Custom exceptions
    public static class InsufficientBuyingPowerException extends RuntimeException {
        public InsufficientBuyingPowerException(String message) {
            super(message);
        }
    }

    public static class InsufficientInventoryException extends RuntimeException {
        public InsufficientInventoryException(String message) {
            super(message);
        }
    }

    public static class OrderNotFoundException extends RuntimeException {
        public OrderNotFoundException(String message) {
            super(message);
        }
    }

    public static class OrderCannotBeCancelledException extends RuntimeException {
        public OrderCannotBeCancelledException(String message) {
            super(message);
        }
    }
}
